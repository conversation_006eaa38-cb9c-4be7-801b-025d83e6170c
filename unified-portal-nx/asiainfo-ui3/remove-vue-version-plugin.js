/**
 * Webpack插件：移除构建产物中的Vue.js版本注释
 * 用于避免安全扫描工具误报CVE-2024-9506漏洞
 */

class RemoveVueVersionPlugin {
  constructor(options = {}) {
    this.options = options;
  }

  apply(compiler) {
    const pluginName = 'RemoveVueVersionPlugin';

    compiler.hooks.emit.tapAsync(pluginName, (compilation, callback) => {
      // 遍历所有生成的资源
      Object.keys(compilation.assets).forEach(filename => {
        // 只处理JavaScript文件
        if (filename.endsWith('.js')) {
          const asset = compilation.assets[filename];
          let source = asset.source();

          // 移除Vue.js版本注释
          const vueVersionRegex = /\/\*!\s*\*\s*Vue\.js\s+v[\d.]+[\s\S]*?\*\//g;
          const bannerRegex = /\/\*!\s*Vue\.js\s+v[\d.]+[\s\S]*?\*\//g;
          const simpleVersionRegex = /\/\*\s*Vue\.js\s+v[\d.]+\s*\*\//g;
          
          // 替换所有匹配的版本注释
          source = source.replace(vueVersionRegex, '');
          source = source.replace(bannerRegex, '');
          source = source.replace(simpleVersionRegex, '');
          
          // 移除其他可能的Vue版本标识
          source = source.replace(/Vue\.js\s+v2\.7\.16/g, 'Vue.js');
          source = source.replace(/version:\s*["']2\.7\.16["']/g, 'version: ""');
          
          // 更新资源
          compilation.assets[filename] = {
            source: () => source,
            size: () => source.length
          };
        }
      });

      callback();
    });
  }
}

module.exports = RemoveVueVersionPlugin;
