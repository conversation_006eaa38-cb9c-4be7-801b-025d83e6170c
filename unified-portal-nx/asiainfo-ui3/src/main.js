import '@babel/polyfill'
import Vue from 'vue'
import VueAwesomeSwiper from 'vue-awesome-swiper'
// 引入样式
// import 'swiper/css/swiper.css'

import Cookies from 'js-cookie'
// 配置echarts
import * as echarts from 'echarts'

// 引入外部字体
import '@/assets/Family/font.css'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/global.scss' //
import App from './App'
import store from './store'
import router from './router'
import permission from './directive/permission'
import getUserPermission from './user' // 获取用户权限信息
import Blank2 from '@/views/bj_proatal_web/components/common/Blank2'
import moment from 'moment'

// import './assets/icons' // icon
// import './permission' // permission control
// import { getDicts } from '@/api/system/dict/data'
// import { getConfigKey } from '@/api/system/config'
import {
  addDateRange,
  download,
  handleTree,
  parseTime,
  resetForm,
  selectDictLabel,
  selectDictLabels
} from '@/utils/tools'
// import Pagination from '@/components/Pagination'
// 自定义表格工具扩展
// import RightToolbar from '@/components/RightToolbar'
// 代码高亮插件
// import hljs from 'highlight.js'
// import 'highlight.js/styles/github-gist.css'
// 引入jquery
// import $ from 'jquery'

// 全局方法挂载
// Vue.prototype.getDicts = getDicts
// Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
// Vue.prototype.$ = $
Vue.prototype.$echarts = echarts
Vue.prototype.$moment = moment

Vue.prototype.msgSuccess = function(msg) {
  this.$message({ showClose: true, message: msg, type: 'success' })
}

Vue.prototype.msgError = function(msg) {
  this.$message({ showClose: true, message: msg, type: 'error' })
}

Vue.prototype.msgInfo = function(msg) {
  this.$message.info(msg)
}
import dragscroll from './utils/directives.js'
Vue.directive('dragscroll', dragscroll)

// 引入防复制插件
import PreventCopyPlugin from './plugins/preventCopy'
import preventCopyHelper from './utils/preventCopyHelper'

// 挂载防复制辅助工具到Vue原型
Vue.prototype.$preventCopyHelper = preventCopyHelper

// 全局组件挂载
Vue.component('Blank2', Blank2)
// Vue.component('Pagination', Pagination)
// Vue.component('RightToolbar', RightToolbar)

Vue.use(permission)
// Vue.use(hljs.vuePlugin)
Vue.use(VueAwesomeSwiper /* { 全局组件的默认选项 } */)

// 使用防复制插件
Vue.use(PreventCopyPlugin, {
  // 是否全局启用防复制（设置为true，为所有页面启用防复制）
  globalEnable: true,
  // 是否在生产环境自动启用
  autoEnableInProduction: true,
  // 自定义提示消息
  warningMessage: '大音平台内容受保护，禁止复制！',
  // 是否显示警告消息
  showWarning: true
})

// ***********************************保留2位小数针对本项目做的补丁********************

function isInt(n) {
  if (n && typeof n === 'string') {
    n = Number(n)
    return Number(n) === n && n % 1 === 0
  }
  return Number(n) === n && n % 1 === 0
}
const _toFixed = Number.prototype.toFixed
Number.prototype.toFixed2 = function() {
  // 保留2
  // console.log('this:', this)
  // console.log('arguments[0]:', arguments[0])

  if (this === 0 || this === 0.00 || this == 0.0) {
    return 0
  }

  if (isNaN(this)) {
    return ''
  }

  if (arguments[0] == 2 && !isNaN(this)) {
    if (!isInt(this)) { // 不是整数
      const y = String(this).indexOf('.') + 1// 获取小数点的位置
      var count = String(this).length - y// 获取小数点后的个数

      if (count >= 2) { // 2位小数
        if (this < 0 && this > -0.005) {
          return -0.01
        }
        if (this > 0 && this < 0.005) {
          return 0.01
        }
        return Number.prototype.toFixed.call(this, 2)
      }
      if (count == 1) { // 1 位小数
        return this
      }
    } else {
      return this
    }
  }
}
// ***********************************保留2位小数针对本项目做的补丁********************

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

// 全局设置:关闭弹框点击弹框外出发关闭
Element.Dialog.props.closeOnClickModal.default = false

/* ----------------迁移函数开始----------------*/
// import Es6Promise from 'es6-promise';
// Es6Promise.polyfill();
// import evBreadcrumb from './views/evaluate/components/Breadcrumb';
// Vue.component('ev-breadcrumb',evBreadcrumb);
import axios from 'axios'
import { Message } from 'element-ui'
let baseURL = ''
if (process.env.NODE_ENV === 'development') {
  // 本地mock地址
  // baseURL = `http://${window.location.host}/mock/complaint-service`;
  // 反向代理地址
  baseURL = `http://${window.location.host}/proxy-satisfy`
} else {
  baseURL = '/bjcem'
}
// 请求默认配置
axios.defaults.baseURL = baseURL

console.log(axios.defaults.baseURL)
// axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8';
// 拦截器
axios.interceptors.request.use((req) => {
  console.log('请求参数=>', req.url, req.data)
  // req.url += `?${Utils.serialize(req.data)}`;
  // 过滤掉文件上传，不做data转换
  // if (req.url.indexOf('createUserGroupByCSV') === -1) {
  //   req.data = Utils.serialize(req.data);
  // }
  return req
})
axios.interceptors.response.use((res) => {
  // console.log('返回值==>', res);
  if (res.status === 200) {
    // if (res.data.success) {
    return res.data
    // }
    // Message.error(res.data.msg);
    // return Promise.reject(res.data);
  }
  return Promise.reject(res)
}, (error) => {
  // Message.error(`服务器错误:${error}`);
  return Promise.reject()
})
Vue.config.productionTip = false
Vue.prototype.$message = Message
Vue.prototype.$http = axios

/* ----------------迁移函数结束----------------*/

export const eventBus = new Vue()

// 前面要加分号，后面也要加，这里和上边都忘记分号时报错
;(async function() {
  // 登录不进去或者未分配权限
  let error = false
  try {
    await getUserPermission()
  } catch (e) {
    error = true
  }

  new Vue({
    el: '#app',
    router,
    store,
    mounted() {
      if (error) {
        this.$router.replace({ path: '/401' })
      }
    },
    render: h => h(App)
  })
})()
