import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
// import Layout from '@/layout'
import { getName } from '@/api/portal/charts'
import { checkToken } from '@/api/portal/charts'
import { recordAdd, recordAddTo4a } from '@/api/authority-data'

import timeTool from '@/views/bj_proatal_web/lib/date.js'

import tool from '@/views/bj_proatal_web/utils/utils'

const routerMapChn = {
  'satisfaction': {
    'name': '集团考核服务KPI',
    'routeId': 100101
  },
  'complainReview': {
    'name': '投诉概览',
    'routeId': 100102
  },
  'teleservice': {
    'name': '端到端服务质量',
    'routeId': 100201
  },
  'customer': {
    'name': '客户管理入口',
    'routeId': 200101
  },
  'customergroup': {
    'name': '客群管理入口',
    'routeId': 200102
  },
  'msgtep': {
    'name': '短信模板入口',
    'routeId': 200103
  },
  'MobileNetwork': {
    'name': '手机网络专题展示',
    'routeId': 300201
  },
  'MobileTraffic': {
    'name': '移动网络质量投诉分析展示',
    'routeId': 300202
  },
  'mobilePhoneTariff': {
    'name': '手机资费及营销专题展示',
    'routeId': 300301
  },
  'homeBroadband': {
    'name': '家庭业务投诉分析',
    'routeId': 300501
  },
  'homeBroadbandEr': {
    'name': '家庭业务报障分析',
    'routeId': 300502
  },
  'homeBroadbandLab': {
    'name': '家庭标签运营分析',
    'routeId': 300503
  },
  'homeBroadbandTrend': {
    'name': '家宽标准指标趋势',
    'routeId': 300504
  },
  'homeBroadbandSe': {
    'name': '家宽感知客群分析',
    'routeId': 300505
  },
  'complainPrewarining': {
    'name': '投诉指标监控与预警体系展示',
    'routeId': 300401
  },
  'caMonitor': {
    'name': '异动投诉监控专题展示',
    'routeId': 300402
  },
  'GovStatisfaction': {
    'name': '政企满意度展示',
    'routeId': 300101
  },
  'gov': {
    'name': '政企服务分析',
    'routeId': 300102
  },
  'contactor': {
    'name': '触点服务专题展示',
    'routeId': 300601
  },
  'complaintMonitoring': {
    'name': '服务投诉流程监控',
    'routeId': 300602
  },
  'null': {
    'name': '工单管理',
    'routeId': 500101
  },
  'WorkflowPanel': {
    'name': '工作流看板',
    'routeId': 500102
  },
  'warnTwoType': {
    'name': '两类差错及反悔办理工单统计',
    'routeId': 510118
  },
  'shareFileManager': {
    'name': '共享中心',
    'routeId': 510107
  },

  'threeLevelWarning': {
    'name': '不知情投诉预警',
    'routeId': 510113
  },

  'zhztse': {
    'name': '客户情绪识别',
    'routeId': 510104
  },

  'complainOverFlow': {
    'name': '两类差错及反悔办理投诉',
    'routeId': 510115
  },

  'reportForms': {
    'name': '报表中心',
    'routeId': 510108
  },

  'allmanage':{
    'name': '四级管控',
    'routeId': 510111
  }











}

// import t from './routerMapChinese'
// let x = {}
// t.forEach(i => {
//   const { id, componentName, menuName } = i
//   x = { ...x, [componentName]: { name: menuName, routeId: id }}
//   console.log(x)
// })

Vue.use(Router)

const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

//* ******************************/

// 温馨提示：宁夏大音很多页面上使用到的组件已经有过可以直接引用
// 开发之前请先访问北京大音的系统(链接如下 )  看看  ,会减少你很多工作
// http://bj-portal.asiainfo.work/     控制台输入  document.cookie="LtpaToken=123"  获取访问北京大音系统的权限

function getAuthority(successcb, failcb) {
  const cooker = document.cookie.split('; ').reduce((total, prev) => {
    const [key, value] = prev.split('=')
    return { ...total, [key]: value }
  }, {})

  if (cooker.LtpaToken) {
    getName({
      ltpaToken: cooker.LtpaToken
    }).then((response) => {
      if (response.code === 200) {
        successcb()
      } else {
        failcb()
      }
    }).catch(err => {
      console.log(err)
      failcb()
    })
  } else {
    failcb()
  }
}

let last_check_token_time = 0

function getCheckToken(successcb, failcb, permission) {





  // 获取用户的map权限
  const mapPermission = JSON.parse(sessionStorage.getItem('nx-map-permission') || '{}')
  if (process.env.NODE_ENV == 'production') {
    const now = Date.now()
    if (now - last_check_token_time > 10000) {
      last_check_token_time = now
      checkToken().then((response) => {
        if (response.code === 200) {
          // 权限超过当前权限
          if (permission) {
            if (mapPermission.mapLevel > permission.mapLevel) {
              failcb()
            } else {
              successcb()
            }
          } else {
            successcb() // 开发环境不验证
          }
        } else {
          failcb()
        }
      }).catch(err => {
        console.log(err)
        failcb()
      })
    } else {
      successcb()
    }
  } else {
    // 权限超过当前权限
    console.log(permission, mapPermission)
    if (permission) {
      console.log(mapPermission.mapLevel < permission.mapLevel)
      if (mapPermission.mapLevel > permission.mapLevel) {
        failcb()
      } else {
        successcb()
      }
    } else {
      successcb() // 开发环境不验证
    }
  }
}
let userInfo = null
// 公共路由
export const constantRoutes = [
  {
    path: '/',
    component: () =>
      import('@/views/bj_proatal_web/index')
  },
  // {
  //   path: '/login',
  //   component: (resolve) => require(['@/views/login'], resolve),
  //   hidden: true
  // },
  // 后端重定向金库认证页面
  {
    path: '/jkAuth',
    name: 'jkAuth',
    component: (resolve) => require(['@/views/bj_proatal_web/views/jkAuth/index.vue'], resolve)
  },
  {
    path: '/index',
    component: (resolve) => require(['@/views/bj_proatal_web/index'], resolve),
    hidden: true,
    children: [
      // ********** 宁夏大音 路由写在这里   ******************
      {
        path: '/satisfaction',
        name: 'satisfaction',
        // component: (resolve) => require(['@/views/bj_proatal_web/views/Satisfaction/index.vue'], resolve),
        component: (resolve) => require(['@/views/bj_proatal_web/views/SatisfactionNew/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/satisfactionOld',
        name: 'satisfactionOld',
        component: (resolve) => require(['@/views/bj_proatal_web/views/Satisfaction/index.vue'], resolve),
        // component: (resolve) => require(['@/views/bj_proatal_web/views/SatisfactionNew/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/threeLevelWarning',
        name: 'threeLevelWarning',
        component: (resolve) => require(['@/views/bj_proatal_web/views/threeLevelWarning'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/complainReview',
        name: 'complainReview',
        component: (resolve) => require(['@/views/bj_proatal_web/views/complainReview'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },

      // 主动关怀 客户管理入口 /customer 客户管理
      {
        path: '/customer',
        name: 'customer',
        component: (resolve) => require(['@/views/bj_proatal_web/views/customer/SolicitudeTask'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 主动关怀： 只有省级用户有权限
            mapLevel: 1
          })
        }
      },
      // 满意度三库
      {
        path: '/satisfactionThreeStore',
        name: 'satisfactionThreeStore',
        component: (resolve) => require(['@/views/bj_proatal_web/views/satisfactionThreeStore/index'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/customergroup',
        name: 'customergroup',
        component: (resolve) => require(['@/views/bj_proatal_web/views/customergroup/Index'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 主动关怀： 只有省级用户有权限
            mapLevel: 1
          })
        }
      },

      {
        path: '/msghistory/:code',
        name: 'msghistory',
        component: (resolve) => require(['@/views/bj_proatal_web/views/customergroup/MsgsendHistory'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 主动关怀： 只有省级用户有权限
            mapLevel: 1
          })
        }
      },

      //  客群管理入口 /customergroup
      //  短信模版入口 /msgtep
      // repair analysis
      {
        path: '/repair/:id',
        name: 'repairanalysis',
        component: (resolve) => require(['@/views/bj_proatal_web/views/repairanalysis/Index'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 主动关怀： 只有省级用户有权限
            mapLevel: 1
          })
        }
      },

      // 服务分项情况下钻系列
      {
        path: '/examine-index/serveDetail',
        name: 'ServeDetail',
        component: (resolve) => require(['@/views/bj_proatal_web/views/examine-index/serveDetail'], resolve)
        // beforeEnter: function (to, from, next) {
        //   getAuthority(() => {
        //     next();
        //   }, () => {
        //     next("/blank");
        //   })
        // }
      },

      {
        path: '/msgtep',
        name: 'msgtep',
        component: (resolve) => require(['@/views/bj_proatal_web/views/customergroup/Msgtemp'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 主动关怀： 只有省级用户有权限
            mapLevel: 1
          })
        }
      }, {
        path: '/homePerception',
        name: 'homePerception',
        component: (resolve) => require(['@/views/bj_proatal_web/views/mobilePhoneTariff/components/homePerception'], resolve)
      },
      {
        name: 'WorkflowPanel',
        path: '/WorkflowPanel',
        component: () =>import('@/views/bj_proatal_web/views/Work/WorkflowPanel/index.vue'),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        name: 'warnTwoType',
        path: '/warnTwoType',
        component: () =>import('@/views/bj_proatal_web/views/Work/warnTwoType.vue'),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }

      },
      {
        path: '/complaintMonitoring', // 服务投诉流程监控
        name: 'complaintMonitoring',
        component: (resolve) => require(['@/views/bj_proatal_web/views/contactor/components/complaintMonitoring'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 触点服务专题 - 服务投诉流程监控：只有省级用户才有权限
            mapLevel: 1
          })
        }
      },
      {
        path: '/cityDistribution', // 地市分析
        name: 'cityDistribution',
        component: (resolve) => require(['@/views/bj_proatal_web/views/contactor/components/cityDistribution'], resolve)
      },
      {
        path: '/topComplaint', // TOP投诉
        name: 'topComplaint',
        component: (resolve) => require(['@/views/bj_proatal_web/views/contactor/components/topComplaint'], resolve)
      },
      // 端到端
      {
        path: '/teleservice',
        name: 'teleservice',
        component: (resolve) => require(['@/views/bj_proatal_web/views/teleservice/teleservice'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 手机资费及营销专题
      {
        path: '/mobilePhoneTariff',
        name: 'mobilePhoneTariff',
        component: (resolve) => require(['@/views/bj_proatal_web/views/mobilePhoneTariff/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/GovStatisfaction',
        name: 'GovStatisfaction',
        component: (resolve) => require(['@/views/bj_proatal_web/views/GovStatisfaction/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          }, {
            // 政企满意度专题- 政企满意度展示：只有省级用户才有权限
            mapLevel: 1
          })
        }
      },
      {
        path: '/gov',
        name: 'gov',
        component: (resolve) => require(['@/views/bj_proatal_web/views/GovStatisfaction/indexnew.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/MobileNetwork',
        name: 'MobileNetwork',
        component: (resolve) => require(['@/views/bj_proatal_web/views/MobileNetwork/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/MobileTraffic',
        name: 'MobileTraffic',
        component: (resolve) => require(['@/views/bj_proatal_web/views/MobileNetwork/MobileTraffic.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 投诉指标监控与预警体系
      {
        path: '/complainPrewarining',
        name: 'complainPrewarining',
        component: (resolve) => require(['@/views/bj_proatal_web/views/ComplainPrewarining/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },

      {
        path: '/complainOverFlow',
        name: 'complainOverFlow',
        component: (resolve) => require(['@/views/bj_proatal_web/views/ComplainPrewarining/components/overflow/complainOverflow.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 家宽 家庭业务投诉分析 /homeBroadband
      {
        path: '/homeBroadband',
        name: 'homeBroadband',
        component: (resolve) => require(['@/views/bj_proatal_web/views/HomeBroadband/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 家庭业务报障分析
      {
        path: '/homeBroadbandEr',
        name: 'homeBroadbandEr',
        component: (resolve) => require(['@/views/bj_proatal_web/views/HomeBroadband/homeBroadbandEr.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 家宽标签运营
      {
        path: '/homeBroadbandLab',
        name: 'homeBroadbandLab',
        component: (resolve) => require(['@/views/bj_proatal_web/views/HomeBroadband/homeBroadbandLab.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 家宽标签运营
      {
        path: '/homeBroadbandTrend',
        name: 'homeBroadbandTrend',
        component: (resolve) => require(['@/views/bj_proatal_web/views/HomeBroadband/homeBroadbandTrend.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 家宽感知客群分析
      {
        path: '/homeBroadbandSe',
        name: 'homeBroadbandSe',
        component: (resolve) => require(['@/views/bj_proatal_web/views/HomeBroadband/homeBroadbandSe.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 触点 contactor
      {
        path: '/contactor',
        name: 'contactor',
        component: (resolve) => require(['@/views/bj_proatal_web/views/contactor/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 智慧中台-客户情绪识别
      {
        path: '/zhztse',
        name: 'zhztse',
        component: (resolve) => require(['@/views/bj_proatal_web/views/contactor/zhztse.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/experience',
        name: 'experience',
        component: (resolve) => require(['@/views/bj_proatal_web/views/contactor/experience.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },

      // 投诉预警管理
      {
        path: '/ComplaintManager',
        name: 'ComplaintManager',
        component: (resolve) => require(['@/views/bj_proatal_web/views/ComplaintManager/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 预警操作页面
      {
        path: '/Complaintoperate',
        name: 'Complaintoperate',
        component: (resolve) => require(['@/views/bj_proatal_web/views/ComplaintManager/operate.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 异动投诉监控
      {
        path: '/caMonitor',
        name: 'caMonitor',
        component: (resolve) => require(['@/views/bj_proatal_web/views/complainMonitor/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 共享中心模块
      {
        path: '/shareFileManager',
        name: 'shareFileManager',
        component: (resolve) => require(['@/views/bj_proatal_web/views/share/report-setting.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 报表
      {
        path: '/reportForms',
        name: 'reportForms',
        component: (resolve) => require(['@/views/bj_proatal_web/views/reportforms/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/impCustomer',
        name: 'impCustomer',
        component: (resolve) => require(['@/views/bj_proatal_web/views/reportforms/impCustomer.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 报表菜单管理
      {
        path: '/reportMenuManagement',
        name: 'reportMenuManagement',
        component: (resolve) => require(['@/views/bj_proatal_web/views/reportforms/menu/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/allmanage',
        name: 'allmanage',
        component: (resolve) => require(['@/views/bj_proatal_web/views/allmanage/index.vue'], resolve)
      },
      // 后端重定向金库认证页面
      {
        path: '/jkAuth',
        name: 'jkAuth',
        component: (resolve) => require(['@/views/bj_proatal_web/views/allmanage/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          // addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/dataSplit',
        name: 'dataSplit',
        component: (resolve) => require(['@/views/bj_proatal_web/views/dataSplit/components/role0.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/question',
        name: 'question',
        component: (resolve) => require(['@/views/bj_proatal_web/views/question/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/warningWorker',
        name: 'warningWorker',
        component: (resolve) => require(['@/views/bj_proatal_web/views/Work/warningWorker.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/homeSatisfied',
        name: 'homeSatisfied',
        component: (resolve) => require(['@/views/bj_proatal_web/views/homeSatisfied/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path: '/tarAna',
        name: 'tarAna',
        component: (resolve) => require(['@/views/bj_proatal_web/views/homeSatisfied/tarAna.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path:'/caseBase',
        name: 'caseBase',
        component: (resolve) => require(['@/views/bj_proatal_web/views/caseBase/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },

      {
        path:'/blackpot',
        name: 'blackpot',
        component: (resolve) => require(['@/views/bj_proatal_web/views/blackpot/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      {
        path:'/workflowSetting',
        name: 'workflowSetting',
        component: (resolve) => require(['@/views/bj_proatal_web/views/workflowSetting/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        }
      },
      // 9类投诉预警监控
      {
        path: '/9ComplaintWarningMonitors',
        name: '9ComplaintWarningMonitors',
        component: (resolve) => require(['@/views/bj_proatal_web/views/warnConfig/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      // 手机号查找
      {
        path: '/phoneSearch',
        name: 'phoneSearch',
        component: (resolve) => require(['@/views/bj_proatal_web/views/phoneSearch/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      // 问卷分析
      {
        path: '/questionAnalysis',
        name: 'questionAnalysis',
        component: (resolve) => require(['@/views/bj_proatal_web/views/questionAnalysis/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/verticalData',
        name: 'verticalData',
        component: (resolve) => require(['@/views/bj_proatal_web/views/extend/VerticalDataManager.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/extendIndex',
        name: 'extendIndex',
        component: (resolve) => require(['@/views/bj_proatal_web/views/extend/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/extendIndex/:businessType',
        name: 'extendIndexWithBusinessType',
        component: (resolve) => require(['@/views/bj_proatal_web/views/extend/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        props: true,
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/extendIndex/:businessType/:businessId',
        name: 'extendIndexWithParams',
        component: (resolve) => require(['@/views/bj_proatal_web/views/extend/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        props: true,
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/extendBusinessTypeConfig',
        name: 'extendBusinessTypeConfig',
        component: (resolve) => require(['@/views/bj_proatal_web/views/extend/BusinessTypeConfig.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/reportConfig',
        name: 'reportConfig',
        component: (resolve) => require(['@/views/bj_proatal_web/views/reportforms/config/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      {
        path: '/reportQuery',
        name: 'reportQuery',
        component: (resolve) => require(['@/views/bj_proatal_web/views/reportforms/query/index.vue'], resolve),
        beforeEnter: function(to, from, next) {
          addPageViewRecord(to, from, next);
          getCheckToken(() => {
            next()
          }, () => {
            next('/401')
          })
        },
        meta:{
          keepAlive:false
        }
      },
      //* ******************************/

      // 温馨提示：宁夏大音很多页面上使用到的组件已经有过可以直接引用
      // 开发之前请先访问北京大音的系统(链接如下 )  看看  ,会减少你很多工作，北京大音的代码如下路由可供参考引用
      // http://bj-portal.asiainfo.work/     控制台输入  document.cookie="LtpaToken=123"  获取访问北京大音系统的权限
      // document.cookie="LtpaToken=123"

      // 以下代码是北京大音项目的路由,宁夏大音项目很多模块都可以参考北京大音

      // {
      //   path: '/home/<USER>/:date',
      //   name: 'home',
      //   component: (resolve) => require(['@/views/bj_proatal_web/views/Home'], resolve),
      //   beforeEnter: function(to, from, next) {
      //     getAuthority(() => {
      //       next()
      //     }, () => {
      //       next('/blank')
      //     })
      //   }
      // },

    ]
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true,
    beforeEnter: function(to, from, next) {
      try {
        // 清除浏览器的cookie sessionStorage localstorang
        document.cookie.split(';')
          .forEach(cookie => document.cookie = cookie.replace(/^ +/, '')
            .replace(/=.*/, `=;expires=${new Date(0).toUTCString()};path=/`))

        // 清除所有sessstorage
        sessionStorage.clear()
        // 清除浏览器所有localstorage
      } catch (error) {

      }
      next()
    }
  },
  { path: '*', redirect: '/404' }
]

const r = new Router({
  mode: 'hash', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes

})

function addPageViewRecord(to,from,next){

  if (!userInfo) {
    userInfo = JSON.parse(localStorage.getItem('userInfo'))
  }
  console.log('to:==>', to)
  console.log('userInfo:==>', userInfo)
  // if (!userInfo) return

  if (userInfo && routerMapChn.hasOwnProperty(to.name)) {
    const p = {
      eventType: 'PAGE_VIEW',
      // id: userInfo.id,
      // loginUser: userInfo.name,
      pageName: routerMapChn[to.name].name,
      routeId: routerMapChn[to.name].routeId
      // time: timeTool.formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss')
    }

    recordAdd(p).then(res => {
      console.log('res:', res)
    })

    try {
      recordAddTo4a({
        optTime: timeTool.formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        pracct: sessionStorage.getItem('mainId'), // 从url中取mainId
        slacct: sessionStorage.getItem('loginName'), // loginName
        result: 0,
        despCmd: `${routerMapChn[to.name].name}`,
        ticket: sessionStorage.getItem('Authorization'), // 取token
        subAcctIsSensitive: 0, // 是否涉及敏感
        operateContent: `查看${routerMapChn[to.name].name}`,
        operateContentIsSensitive: 0,
        isBlur: 0, // 是否模糊化
        isBatch: 0,
        involvingPhone: '',
        operationType: 4

      })
    } catch (error) {

    }
  }


}


// r.beforeEach((to, from, next) => {
//   if (!userInfo) {
//     userInfo = JSON.parse(localStorage.getItem('userInfo'))
//   }
//   console.log('to:==>', to)
//   console.log('userInfo:==>', userInfo)
//   // if (!userInfo) return

//   if (userInfo && routerMapChn.hasOwnProperty(to.name)) {
//     const p = {
//       eventType: 'PAGE_VIEW',
//       // id: userInfo.id,
//       // loginUser: userInfo.name,
//       pageName: routerMapChn[to.name].name,
//       routeId: routerMapChn[to.name].routeId
//       // time: timeTool.formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss')
//     }

//     recordAdd(p).then(res => {
//       console.log('res:', res)
//     })

//     try {
//       recordAddTo4a({
//         optTime: timeTool.formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss'),
//         pracct: sessionStorage.getItem('mainId'), // 从url中取mainId
//         slacct: sessionStorage.getItem('loginName'), // loginName
//         result: 0,
//         despCmd: `${routerMapChn[to.name].name}`,
//         ticket: sessionStorage.getItem('Authorization'), // 取token
//         subAcctIsSensitive: 0, // 是否涉及敏感
//         operateContent: `查看${routerMapChn[to.name].name}`,
//         operateContentIsSensitive: 0,
//         isBlur: 0, // 是否模糊化
//         isBatch: 0,
//         involvingPhone: '',
//         operationType: 4

//       })
//     } catch (error) {

//     }
//   }

//   // do someting
//   next()
// })

export default r
