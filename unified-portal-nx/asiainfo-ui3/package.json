{"name": "srd-bd2-boot", "version": "1.0.0", "description": "大音平台", "author": "srd-bd2", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:local": "vue-cli-service build --mode local", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "http://***********:8889/appproject/base/asiainfo-srd-bd-boot.git"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@riophae/vue-treeselect": "0.4.0", "@vuikit/icons": "^0.8.1", "@vuikit/theme": "^0.8.1", "@wangeditor-next/editor": "^5.6.45", "@wangeditor-next/editor-for-vue2": "^1.0.2", "axios": "0.21.0", "clipboard": "2.0.6", "core-js": "3.8.1", "crypto-js": "^4.1.1", "dom7": "^4.0.0", "echarts": "^5.2.1", "element-ui": "^2.15.6", "file-saver": "2.0.4", "fuse.js": "6.4.3", "html2canvas": "^1.4.1", "jquery": "^3.5.1", "jsencrypt": "3.0.0-rc.1", "jspdf": "^2.5.1", "moment": "^2.29.4", "nanoid": "^3.0.0", "path-to-regexp": "6.2.0", "resize-detector": "^0.3.0", "screenfull": "5.0.2", "slate": "^0.82.0", "sortablejs": "1.10.2", "swiper": "^8.0.7", "vue": "^2.7.16", "vue-awesome-swiper": "^3.1.3", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-echarts": "^6.0.0", "vue-router": "^3.0.7", "vue-upload-component": "^2.8.22", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "@vue/composition-api": "^1.2.4", "babel-eslint": "10.1.0", "chalk": "4.1.0", "compression-webpack-plugin": "^1.1.12", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "js-beautify": "1.13.0", "js-cookie": "^3.0.1", "less": "^4.1.2", "less-loader": "^4.1.0", "lint-staged": "10.5.3", "node-sass": "^4.14.1", "runjs": "4.4.2", "sass-loader": "^7.3.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "terser-webpack-plugin": "^5.3.14", "vue-template-compiler-patched": "^2.7.16-patch.2"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}