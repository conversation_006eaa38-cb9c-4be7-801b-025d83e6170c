#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;
    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server_tokens off;

    # SSL配置 - 全局SSL设置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_ecdh_curve secp384r1;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    #8080 宁夏大音门户 此端口承载网能访问
    server {
        listen 8080;
        server_name **************;
        if ($http_Host !~* ^**************:8080$)
        {
           return 403;
        }

        location / {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/dist;
            index index.html index.htm;
        }

        error_page  404 /404.html;
        location = /404.html {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/;
        }

        location /nxportal {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            rewrite ^.+nxportal/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:8085;
            proxy_buffer_size 4096k;
            proxy_buffers 16 4096k;
            proxy_busy_buffers_size 4096k;
            proxy_temp_file_write_size 4096k;
        }
        ## 宁夏大音门户-test一个反向代理
        location /portal/login/getFromOaLogin {
            include  uwsgi_params;
            client_max_body_size 20m;
            proxy_pass   http://**************:8085/portal/login/getFromOaLogin;
        }

        #工作流看板页面
        location /apiWorkBoard {
            client_max_body_size 500m;
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            rewrite ^.+apiWorkBoard/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:10101;
        }

        #地图服务转发
        location /Grid {
            proxy_pass http://10.254.123.75:7001;
        }
        location /grid {
            proxy_pass http://10.254.123.75:7001;
        }
    }

    #8090 宁夏大音工作流 此端口承载网能访问
    server {
        listen 8090;

        location / {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/workflow;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/workflow;
        }

        location /api {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^.+api/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:10101;
        }
        location /swaggerdoc {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            rewrite ^.+swaggerdoc/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:10101;
        }
    }

    # 8081 宁夏大音问卷(互动调研) 端口不对外,省内跳板机使用此端口
    server {
        listen 8081;

        location / {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/dist/question;
            index index.html index.htm;
        }

        error_page  404 /404.html;
        location = /404.html {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/;
        }

        location /nxportal {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            rewrite ^.+nxportal/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:8085;
        }
    }

    #8091 H5页面,网格通及moa使用此端口
    server {
        listen 8091;
        location / {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/mobile;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/mobile;
        }
        #网格通及moa代理到工作流
        location /api {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^.+api/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:10101;
        }
        #网格通及moa代理到门户
        location /api2 {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^.+api2/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:8085;
        }
    }

    #18080 宁夏大音门户uat 此端口承载网能访问
    server {
        listen 18080;
        server_name **************;
        if ($http_Host !~* ^**************:18080$)
        {
           return 403;
        }
        location / {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/gray/dist;
            index index.html index.htm;
        }
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html/gray/dist;
        }
        location /nxportal {
            client_max_body_size 500m;
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            rewrite ^.+nxportal/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:18085;
            proxy_buffer_size 1024k;
            proxy_buffers 16 1024k;
            proxy_busy_buffers_size 2048k;
            proxy_temp_file_write_size 2048k;
        }
        ##宁夏大音门户-test一个反向代理
        location /portal/login/getFromOaLogin {
                include  uwsgi_params;
                client_max_body_size 20m;
                proxy_pass   http://**************:18085/portal/login/getFromOaLogin;
        }
        #地图转发
        location /Grid {
            proxy_pass http://10.254.123.75:7001;
        }
        location /grid {
            proxy_pass http://10.254.123.75:7001;
        }
    }
    #18090 工作流uat 此端口承载网能访问
    server {
        listen 18090;
        location / {
            root /home/<USER>/shanghuichao/nginx-1.24.0/html/gray/workflow;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html/gray/workflow;
        }

        location /api {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^.+api/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:20108;
        }
        location /graydoc {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            rewrite ^.+graydoc/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://**************:20108;
        }
        location /test {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_set_header Host $host;
            rewrite ^.+test/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://127.0.0.1:9999;
        }
    }
}