# 横表数据管理系统操作手册

## 系统概述

横表数据管理系统是一个基于Vue.js的动态数据管理平台，支持根据业务类型配置自动生成数据管理界面。系统通过配置化的方式，让用户可以灵活定义不同的业务类型和字段，无需修改代码就能支持新的业务需求。

## 主要功能

### 1. 横表数据管理页面 (index.vue)
- **动态业务类型选择**：支持根据业务类型动态展示不同的字段和界面
- **灵活的数据查询**：支持业务ID查询和动态字段条件查询
- **数据表格展示**：自动根据业务类型生成对应的表格列
- **完整的数据操作**：新增、编辑、查看、删除、批量删除
- **Excel导入导出**：模板下载和批量导入功能
- **分页浏览**：支持大数据量的分页展示

### 2. 业务类型配置页面 (BusinessTypeConfig.vue)
- **业务类型管理**：新增、编辑、删除业务类型
- **字段配置**：支持6种字段类型（文本、数字、布尔、日期、选择器、多行文本）
- **批量操作**：批量启用、禁用、删除业务类型
- **导入导出**：JSON配置文件和Excel文件导入导出
- **搜索筛选**：关键词搜索和状态筛选

## 详细操作指南

### 横表数据管理页面操作

#### 1. 业务类型选择
- **操作步骤**：
  1. 在页面顶部的查询表单中，找到"业务类型"下拉框
  2. 点击下拉框，选择需要管理的业务类型
  3. 系统会自动加载该业务类型的数据和字段配置

- **注意事项**：
  - 必须先选择业务类型才能进行数据操作
  - 业务类型选项从业务类型配置页面获取

#### 2. 数据查询
- **基础查询**：
  1. 在"业务ID"输入框中输入要查询的业务ID
  2. 点击"查询"按钮进行搜索

- **高级查询**：
  1. 系统会根据业务类型配置动态显示字段查询条件
  2. 在相应的字段输入框中输入查询条件
  3. 支持文本、数字、日期、布尔值等多种字段类型的查询

- **重置查询**：
  1. 点击"重置"按钮清空所有查询条件
  2. 系统会保留当前选择的业务类型

#### 3. 数据操作

##### 新增数据
1. 点击"新增"按钮
2. 在弹出的对话框中选择业务类型（新增时必选）
3. 填写各个字段的数据
4. 添加备注信息（可选）
5. 点击"确定"按钮保存数据

##### 编辑数据
1. 在数据表格中找到要编辑的行
2. 点击该行的"编辑"按钮
3. 在弹出的对话框中修改数据
4. 业务类型在编辑模式下不可更改
5. 点击"确定"按钮保存修改

##### 查看数据
1. 在数据表格中找到要查看的行
2. 点击该行的"查看"按钮
3. 在弹出的详情对话框中查看完整数据
4. 包含业务信息、字段数据、创建时间、更新人等

##### 删除数据
1. 在数据表格中找到要删除的行
2. 点击该行的"删除"按钮
3. 在确认对话框中点击"确定"确认删除
4. 系统会删除该条数据

##### 批量删除
1. 在数据表格中勾选要删除的数据行（可多选）
2. 点击"批量删除"按钮
3. 在确认对话框中点击"确定"确认批量删除
4. 系统会删除所有选中的数据

#### 4. Excel导入导出

##### 下载模板
1. 选择要导入数据的业务类型
2. 点击"下载模板"按钮
3. 系统会生成对应业务类型的Excel模板
4. 模板包含所有需要填写的字段列

##### 批量导入
1. 选择要导入数据的业务类型
2. 点击"批量导入"按钮
3. 在弹出的对话框中点击"选择文件"
4. 选择填写完成的Excel文件
5. 点击"开始导入"按钮
6. 系统会验证并导入数据

- **导入注意事项**：
  - 文件格式必须是.xlsx或.xls
  - 文件大小不能超过10MB
  - 必须使用系统提供的模板
  - 确保数据格式正确

#### 5. 分页操作
- **调整每页显示数量**：
  1. 在分页组件中选择每页显示条数
  2. 支持10、20、50、100条每页

- **翻页操作**：
  1. 点击页码跳转到指定页面
  2. 使用"上一页"、"下一页"按钮浏览
  3. 在跳转输入框中输入页码直接跳转

### 业务类型配置页面操作

#### 1. 业务类型管理

##### 新增业务类型
1. 点击"新增业务类型"按钮
2. 在弹出的对话框中填写业务类型信息：
   - 业务类型编码：唯一标识符
   - 业务类型名称：显示名称
   - 备注：描述信息
3. 点击"确定"保存配置

##### 编辑业务类型
1. 在业务类型列表中找到要编辑的类型
2. 点击"编辑"按钮
3. 修改业务类型信息
4. 点击"确定"保存修改

##### 删除业务类型
1. 在业务类型列表中找到要删除的类型
2. 点击"删除"按钮
3. 在确认对话框中点击"确定"确认删除
4. 注意：删除业务类型会同时删除相关数据

#### 2. 字段配置

##### 支持的字段类型：
- **文本字段**：用于存储文本信息
- **数字字段**：用于存储数值，支持小数
- **布尔字段**：用于存储是/否值
- **日期字段**：用于存储日期时间
- **选择器字段**：提供预设选项供选择
- **多行文本**：用于存储长文本内容

##### 字段属性配置：
- **字段名称**：数据字段标识符
- **字段标签**：显示名称
- **字段类型**：选择合适的字段类型
- **是否必填**：设置是否为必填字段
- **默认值**：设置字段的默认值
- **验证规则**：设置数据验证条件

#### 3. 批量操作
- **批量启用**：选择多个业务类型，点击"批量启用"
- **批量禁用**：选择多个业务类型，点击"批量禁用"
- **批量删除**：选择多个业务类型，点击"批量删除"

#### 4. 导入导出配置

##### 导出配置
1. 选择要导出的业务类型
2. 点击"导出配置"按钮
3. 选择导出格式（JSON或Excel）
4. 系统会生成配置文件

##### 导入配置
1. 点击"导入配置"按钮
2. 选择配置文件（JSON或Excel）
3. 系统会验证并导入配置

#### 5. 搜索和筛选
- **关键词搜索**：在搜索框中输入关键词进行搜索
- **状态筛选**：根据启用/禁用状态筛选业务类型

## 系统特色功能

### 1. 动态配置驱动
- 根据业务类型配置自动生成界面
- 无需修改代码就能支持新的业务需求
- 支持实时配置更新

### 2. 灵活字段类型
- 支持6种字段类型
- 每种字段类型都有专门的输入控件
- 支持字段验证和默认值

### 3. 高效批量操作
- 支持批量数据删除
- 支持批量业务类型管理
- 提高数据管理效率

### 4. 数据迁移支持
- 完整的Excel导入导出功能
- 支持配置文件的导入导出
- 方便数据备份和迁移

### 5. 响应式设计
- 适配不同屏幕尺寸
- 支持移动端访问
- 良好的用户体验

## 常见问题解答

### Q1: 为什么无法选择业务类型？
A1: 请确保已在业务类型配置页面中添加了业务类型，并且状态为启用。

### Q2: Excel导入失败怎么办？
A2: 请检查：
- 文件格式是否正确（.xlsx或.xls）
- 文件大小是否超过10MB
- 是否使用了正确的模板
- 数据格式是否符合要求

### Q3: 如何添加新的字段类型？
A3: 目前系统支持6种字段类型，如需添加新的类型，请联系系统管理员。

### Q4: 删除业务类型会有什么影响？
A4: 删除业务类型会同时删除该类型下的所有数据，请谨慎操作。

### Q5: 如何备份数据？
A5: 可以通过Excel导出功能备份数据，也可以导出业务类型配置文件。

## 快捷键说明

- **Ctrl + N**: 新增数据
- **Ctrl + F**: 聚焦搜索框
- **Ctrl + R**: 刷新数据
- **Delete**: 删除选中数据
- **Esc**: 关闭对话框

## 系统要求

- **浏览器**: Chrome、Firefox、Safari、Edge等现代浏览器
- **网络**: 需要稳定的网络连接
- **屏幕分辨率**: 建议最低1024×768

## 联系支持

如遇到系统使用问题或有功能建议，请联系：
- 技术支持：技术支持邮箱
- 系统管理员：管理员联系方式
- 问题反馈：反馈渠道

---

*本操作手册最后更新时间：2025年1月*