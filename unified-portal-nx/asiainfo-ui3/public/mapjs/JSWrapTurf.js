// 动态加载JS的函数
function loadScript(src, callback) {
  var script = document.createElement("script");
  var head = document.getElementsByTagName("head")[0];
  script.type = "text/javascript";
  script.charset = "UTF-8";
  script.src = src;
  if (script.addEventListener) {
    script.addEventListener(
      "load",
      function () {
        callback();
      },
      false
    );
  } else if (script.attachEvent) {
    script.attachEvent("onreadystatechange", function () {
      var target = window.event.srcElement;
      if (target.readyState == "loaded") {
        callback();
      }
    });
  }
  head.appendChild(script);
}
function getContextPath2() {
  var pathName = document.location.pathname;
  var index = pathName.substr(1).indexOf("/");
  var result = pathName.substr(0, index + 1);
  return result;
}

// mapbox官网---js
// loadScript('https://api.tiles.mapbox.com/mapbox-gl-js/v1.1.1/mapbox-gl.js' , function(){
// loadScript('../js/mapbox-gl-js/v2.2.0/mapbox-gl.js' , function(){}   );

try {
  console.log(window.location.href);
  if (window.location.href.indexOf("https://wgt.nx.chinamobile.com") != -1) {
    loadScript(
      "https://wgt.nx.chinamobile.com:9002/Grid/SDKService/jssdk/4.0/CMMap.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71",
      function () {}
    );
  } else {
    // loadScript(
    //   "http://**************:8080/Grid/SDKService/jssdk/4.0/CMMap.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71",
    //   function () {}
    // );
    loadScript(
      "http://**************:8080/Grid/SDKService/jssdk/4.0/CMMap.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71",
      function () {}
    );
  }
} catch (e) {
  // loadScript(
  //   "http://**************:8080/Grid/SDKService/jssdk/4.0/CMMap.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71",
  //   function () {}
  // );
  loadScript(
    "http://**************:8080/Grid/SDKService/jssdk/4.0/CMMap.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71",
    function () {}
  );
}
// 加载 dist 目录，默认会加载同级目录下的 Config.js 文件
// loadScript('http://************:7105/Grid/SDKService/jssdk/4.0/CMMap.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71', function(){} );

// loadScript(getContextPath2()+'/pages/CMmap/js/CMMap-4.0.js?v=4.0&key=aaa7bec7dn804x3199mu63i2s1az4w71', function(){} );
